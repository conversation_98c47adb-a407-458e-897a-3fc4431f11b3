import { doc, getDoc, setDoc, runTransaction } from 'firebase/firestore';
import { db } from '../firebase/firestore.js';
import { type DailyMissionProgress, type UserStreakData, MissionMetric } from '../types/mission.types.js';
import { MISSION_CATALOG, getDailyMissions } from './missionCatalog.js';
import posthog from 'posthog-js';
import { browser } from '$app/environment';

/**
 * Get today's date key in YYYY-MM-DD format
 */
export function getTodayKey(): string {
	return new Date().toISOString().split('T')[0];
}

/**
 * Get this week's key in YYYY-WW format
 */
export function getWeekKey(): string {
	const now = new Date();
	const year = now.getFullYear();
	const week = Math.ceil(((now.getTime() - new Date(year, 0, 1).getTime()) / 86400000 + new Date(year, 0, 1).getDay() + 1) / 7);
	return `${year}-${week.toString().padStart(2, '0')}`;
}

/**
 * Initialize daily progress document for today
 */
export async function initializeDailyProgress(userId: string): Promise<DailyMissionProgress> {
	const todayKey = getTodayKey();
	const dailyMissions = getDailyMissions();
	
	const initialProgress: DailyMissionProgress = {
		missions: {},
		completed: false,
		createdAt: new Date()
	};

	// Initialize all daily missions with 0 progress
	dailyMissions.forEach(mission => {
		initialProgress.missions[mission.id] = 0;
	});

	const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);
	await setDoc(progressRef, initialProgress);
	
	return initialProgress;
}

/**
 * Check if all daily missions are complete
 */
export function checkAllMissionsComplete(progress: DailyMissionProgress): boolean {
	const dailyMissions = getDailyMissions();
	
	return dailyMissions.every(mission => {
		const currentProgress = progress.missions[mission.id] || 0;
		return currentProgress >= mission.target;
	});
}

/**
 * Calculate streak based on completion history
 */
export function calculateStreak(lastMissionDate: string | undefined): number {
	if (!lastMissionDate) return 0;
	
	const today = getTodayKey();
	const lastDate = new Date(lastMissionDate);
	const todayDate = new Date(today);
	
	// Calculate days difference
	const diffTime = todayDate.getTime() - lastDate.getTime();
	const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
	
	// If more than 1 day gap, streak is broken
	if (diffDays > 1) return 0;

	// If exactly 1 day, continue streak
	// If same day, maintain current streak
	return diffDays <= 1 ? 1 : 0;
}

/**
 * Migrate existing progress document to include new missions
 */
export async function migrateMissionProgress(userId: string, progress: DailyMissionProgress): Promise<DailyMissionProgress> {
	const dailyMissions = getDailyMissions();
	let needsUpdate = false;

	// Add any missing missions with 0 progress
	dailyMissions.forEach(mission => {
		if (!(mission.id in progress.missions)) {
			progress.missions[mission.id] = 0;
			needsUpdate = true;
		}
	});

	// Recalculate completion status
	const allComplete = checkAllMissionsComplete(progress);
	if (progress.completed !== allComplete) {
		progress.completed = allComplete;
		if (allComplete && !progress.completedAt) {
			progress.completedAt = new Date();
		} else if (!allComplete && progress.completedAt) {
			// Remove completedAt field when missions are no longer complete
			delete progress.completedAt;
		}
		needsUpdate = true;
	}

	// Update document if needed
	if (needsUpdate) {
		const todayKey = getTodayKey();
		const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);
		await setDoc(progressRef, progress);
	}

	return progress;
}

/**
 * Increment mission progress for a specific metric
 */
export async function incrementMissionProgress(
	userId: string,
	metric: MissionMetric,
	increment: number = 1
): Promise<void> {
	const todayKey = getTodayKey();

	const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);

	try {
		await runTransaction(db, async (transaction) => {
			// Get current progress
			const progressDoc = await transaction.get(progressRef);
			let progress: DailyMissionProgress;
			let isNewDocument = false;

			if (!progressDoc.exists()) {
				// Initialize if doesn't exist - create within transaction to avoid race condition
				const dailyMissions = getDailyMissions();
				progress = {
					missions: {},
					completed: false,
					createdAt: new Date()
				};

				// Initialize all daily missions with 0 progress
				dailyMissions.forEach(mission => {
					progress.missions[mission.id] = 0;
				});
				isNewDocument = true;
			} else {
				progress = progressDoc.data() as DailyMissionProgress;
			}

			// Find missions that use this metric
			const relevantMissions = MISSION_CATALOG.filter(mission => mission.metric === metric);
			let progressUpdated = false;

			// Update progress for relevant missions
			relevantMissions.forEach(mission => {
				const currentProgress = progress.missions[mission.id] || 0;
				const newProgress = Math.min(currentProgress + increment, mission.target);
				
				if (newProgress !== currentProgress) {
					progress.missions[mission.id] = newProgress;
					progressUpdated = true;

					// Track mission completion if just completed
					if (newProgress >= mission.target && currentProgress < mission.target && browser && posthog) {
						posthog.capture('mission_completed', {
							mission_id: mission.id,
							mission_name: mission.name,
							target_value: mission.target,
							completion_time: new Date().toISOString()
						});
					}
				}
			});

			// Check if all missions are now complete
			const allComplete = checkAllMissionsComplete(progress);
			
			if (allComplete && !progress.completed) {
				progress.completed = true;
				progress.completedAt = new Date();

				// Update streak data in separate document
				const streakRef = doc(db, 'users', userId, 'missionProgress', 'streakData');
				const streakDoc = await transaction.get(streakRef);

				const streakData: UserStreakData = streakDoc.exists()
					? streakDoc.data() as UserStreakData
					: {
						currentStreak: 0,
						bestStreak: 0
					};

				// Calculate new streak
				const previousStreak = streakData.currentStreak;
				const newStreak = streakData.lastMissionDate === getTodayKey()
					? streakData.currentStreak
					: streakData.currentStreak + 1;

				const updatedStreakData: UserStreakData = {
					currentStreak: newStreak,
					bestStreak: Math.max(newStreak, streakData.bestStreak),
					lastMissionDate: todayKey
				};

				// Track streak milestone if reached and PostHog is available
				if (browser && posthog && newStreak > previousStreak) {
					const milestones = [3, 7, 14, 30, 50, 100];
					const newMilestone = milestones.find(
					milestone => newStreak >= milestone && previousStreak < milestone
					);
		
					if (newMilestone) {
					posthog.capture('streak_milestone_reached', {
						milestone: newMilestone,
						previous_streak: previousStreak
					});
					}
				}
      
				// Update user properties for PostHog
				if (browser && posthog) {
					posthog.setPersonProperties({
						current_streak: newStreak,
						best_streak: updatedStreakData.bestStreak,
						total_missions_completed: Object.values(progress.missions).reduce((sum, val) => sum + val, 0)
					});
				}

				transaction.set(streakRef, updatedStreakData);
			}

			// Update progress document
			if (isNewDocument || progressUpdated || allComplete) {
				transaction.set(progressRef, progress);
			}
		});
	} catch (error) {
		console.error('Error incrementing mission progress:', error);
		throw error;
	}
}

/**
 * Reset streak if needed (call this on app initialization)
 */
export async function resetStreakIfNeeded(userId: string): Promise<void> {
	const streakRef = doc(db, 'users', userId, 'missionProgress', 'streakData');

	try {
		const streakDoc = await getDoc(streakRef);

		if (!streakDoc.exists()) {
			// No streak data exists yet, nothing to reset
			return;
		}

		const streakData: UserStreakData = streakDoc.data() as UserStreakData;

		if (streakData.lastMissionDate) {
			const daysSinceLastMission = calculateStreak(streakData.lastMissionDate);

			if (daysSinceLastMission === 0 && streakData.currentStreak > 0) {
				// Track streak broken
				if (browser && posthog) {
					posthog.capture('streak_broken', {
						streak_length: streakData.currentStreak
					});

										posthog.setPersonProperties({
						current_streak: 0,
						best_streak: streakData.bestStreak
					});
				}

				// Streak broken, reset to 0
				const updatedStreakData: UserStreakData = {
					...streakData,
					currentStreak: 0
				};

				// Update user properties
				if (browser && posthog) {

				}

				await setDoc(streakRef, updatedStreakData);
			}
		}
	} catch (error) {
		console.error('Error resetting streak:', error);
	}
}
